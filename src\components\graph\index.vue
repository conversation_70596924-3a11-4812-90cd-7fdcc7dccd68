<template>
  <div ref="wrapRef" class="graph-wrap">
    <svg ref="svgRef" class="graph-svg">
      <!-- 箭头定义 -->
      <defs>
        <marker id="arrow" viewBox="0 -5 10 10" refX="16" refY="0" markerWidth="6" markerHeight="6" orient="auto">
          <path d="M0,-5L10,0L0,5" />
        </marker>
      </defs>

      <!-- 缩放容器 -->
      <g ref="zoomLayer">
        <!-- 连线（曲线 path） -->
        <g class="links"></g>
        <!-- 边文字 -->
        <g class="link-labels"></g>
        <!-- 节点 -->
        <g class="nodes"></g>
      </g>
    </svg>

    <!-- 简单 Tooltip -->
    <div v-show="tooltip.show" class="tooltip" :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }">
      <div class="tt-name">{{ tooltip.data?.nodeName || tooltip.data?.id }}</div>
      <div class="tt-sub" v-if="tooltip.data?.entityId">ID: {{ tooltip.data.entityId }}</div>
      <div class="tt-sub" v-if="tooltip.data?.label">类型: {{ tooltip.data.label }}</div>
    </div>
  </div>
</template>

<script setup>
/**
 * 用法：
 * <GraphView :nodes="nodes" :links="links" />
 *
 * nodes: [{ id: '0', nodeName: '张伟', label: '军事人员表', ... }]
 * links: [{ id:'39', name:'参与训练', source:'0', target:'30', ... }]
 *
 * 依赖：npm i d3
 */
import { onMounted, onBeforeUnmount, ref, watch, computed } from "vue";
import * as d3 from "d3";

const props = defineProps({
  nodes: { type: Array, required: true },
  links: { type: Array, required: true },
  /** 节点半径 */
  r: { type: Number, default: 16 },
  /** 线条粗细 */
  stroke: { type: Number, default: 2 },
  /** 力导参数 */
  force: {
    type: Object,
    default: () => ({
      charge: -350, // 斥力
      linkDistance: 120, // 连线期望长度
      linkStrength: 0.7, // 线强度
    }),
  },
});

const svgRef = ref(null);
const zoomLayer = ref(null);
const wrapRef = ref(null);

let sim = null;
let resizeObs = null;

// 颜色映射（按 label）
const color = d3.scaleOrdinal(d3.schemeSet2);

const width = ref(800);
const height = ref(600);

const tooltip = ref({ show: false, x: 0, y: 0, data: null });

// 高亮状态
const activeId = ref(null);
const neighbor = new Map(); // key -> Set(邻居 id)

function buildNeighborIndex(nodes, links) {
  neighbor.clear();
  nodes.forEach((n) => neighbor.set(n.id, new Set()));
  links.forEach((l) => {
    neighbor.get(String(l.source))?.add(String(l.target));
    neighbor.get(String(l.target))?.add(String(l.source));
  });
}

function isNeighbor(a, b) {
  if (!a || !b) return false;
  if (a === b) return true;
  return neighbor.get(String(a))?.has(String(b));
}

// 初始化渲染
function render() {
  const svg = d3.select(svgRef.value);
  const g = d3.select(zoomLayer.value);

  // 尺寸
  svg.attr("width", width.value).attr("height", height.value);

  // 缩放
  svg
    .call(
      d3
        .zoom()
        .scaleExtent([0.25, 4])
        .on("zoom", (ev) => g.attr("transform", ev.transform))
    )
    .on("dblclick.zoom", null);

  // 数据拷贝（D3 会写坐标）
  const nodes = props.nodes.map((d) => ({ ...d }));
  const links = props.links.map((d) => ({ ...d }));

  buildNeighborIndex(nodes, links);

  // 线组
  const linkG = g.select(".links");
  const linkLabelG = g.select(".link-labels");
  const nodeG = g.select(".nodes");

  // 渲染连线路径（用二次贝塞尔做轻微弯曲）
  const link = linkG
    .selectAll("path")
    .data(links, (d) => d.id ?? `${d.source}-${d.target}-${d.name}`)
    .join("path")
    .attr("class", "link")
    .attr("marker-end", "url(#arrow)")
    .attr("fill", "none")
    .attr("stroke-width", props.stroke);

  // 边文字：textPath 绑定到 path 上
  const linkLabel = linkLabelG
    .selectAll("text")
    .data(links, (d) => "lbl-" + (d.id ?? `${d.source}-${d.target}-${d.name}`))
    .join("text")
    .attr("class", "link-text")
    .attr("dy", -4)
    .append("textPath")
    .attr("startOffset", "50%")
    .attr("text-anchor", "middle")
    .attr("href", (d, i) => `#${pathId(d, i)}`)
    .text((d) => d.name ?? "");

  // 为每条线设置唯一 id（供 textPath 用）
  link.attr("id", (d, i) => pathId(d, i));

  // 节点
  const node = nodeG
    .selectAll("g.node")
    .data(nodes, (d) => d.id)
    .join((enter) => {
      const group = enter.append("g").attr("class", "node");
      group.append("circle").attr("r", props.r);
      group
        .append("text")
        .attr("class", "node-label")
        .attr("y", props.r + 14)
        .attr("text-anchor", "middle")
        .text((d) => d.nodeName ?? d.id);
      return group;
    });

  node
    .select("circle")
    .attr("fill", (d) => color(d.label))
    .attr("stroke-width", 2);

  // 节点交互
  node
    .on("mouseenter", (ev, d) => {
      const [x, y] = d3.pointer(ev, wrapRef.value);
      tooltip.value = { show: true, x, y, data: d };
    })
    .on("mousemove", (ev) => {
      const [x, y] = d3.pointer(ev, wrapRef.value);
      tooltip.value.x = x;
      tooltip.value.y = y;
    })
    .on("mouseleave", () => {
      tooltip.value.show = false;
    })
    .on("click", (_, d) => {
      activeId.value = activeId.value === d.id ? null : d.id;
      updateHighlight();
    });

  // 拖拽
  node.call(
    d3
      .drag()
      .on("start", (ev, d) => {
        if (!ev.active) sim.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on("drag", (ev, d) => {
        d.fx = ev.x;
        d.fy = ev.y;
      })
      .on("end", (ev, d) => {
        if (!ev.active) sim.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      })
  );

  // 力导
  sim?.stop();
  sim = d3
    .forceSimulation(nodes)
    .force(
      "link",
      d3
        .forceLink(links)
        .id((d) => d.id)
        .distance(props.force.linkDistance)
        .strength(props.force.linkStrength)
    )
    .force("charge", d3.forceManyBody().strength(props.force.charge))
    .force("center", d3.forceCenter(width.value / 2, height.value / 2))
    .force("collision", d3.forceCollide(props.r + 4))
    .on("tick", ticked);

  function ticked() {
    // 曲线：使用二次贝塞尔（Q）让线更自然
    link.attr("d", (d) => {
      const sx = d.source.x,
        sy = d.source.y;
      const tx = d.target.x,
        ty = d.target.y;
      const dx = tx - sx,
        dy = ty - sy;
      const mx = sx + dx / 2,
        my = sy + dy / 2;
      // 垂直方向轻微偏移
      const norm = Math.hypot(dx, dy) || 1;
      const off = 20;
      const cx = mx - (dy / norm) * off;
      const cy = my + (dx / norm) * off;
      return `M${sx},${sy} Q${cx},${cy} ${tx},${ty}`;
    });

    node.attr("transform", (d) => `translate(${d.x},${d.y})`);
  }

  function updateHighlight() {
    const aid = activeId.value;
    const isActive = (d) => !aid || d.id === aid || isNeighbor(aid, d.id);

    node
      .select("circle")
      .attr("opacity", (d) => (isActive(d) ? 1 : 0.2))
      .attr("stroke", (d) => (d.id === aid ? "#111" : "#fff"));

    node.select("text").attr("opacity", (d) => (isActive(d) ? 1 : 0.2));

    link
      .attr("opacity", (d) => {
        if (!aid) return 0.9;
        return d.source.id === aid || d.target.id === aid ? 0.95 : 0.15;
      })
      .attr("stroke", (d) => (d.source.id === aid || d.target.id === aid ? "#2ca02c" : "#999"));

    linkLabelG.selectAll("text").attr("opacity", (d) => {
      if (!aid) return 0.9;
      return d.source.id === aid || d.target.id === aid ? 1 : 0.2;
    });
  }

  updateHighlight();
}

// 唯一路径 id（提供给 textPath 绑定）
function pathId(d, i) {
  const sid = typeof d.source === "object" ? d.source.id : d.source;
  const tid = typeof d.target === "object" ? d.target.id : d.target;
  return `path-${sid}-${tid}-${d.id ?? i}`;
}

function measure() {
  const rect = wrapRef.value.getBoundingClientRect();
  width.value = Math.max(300, rect.width);
  height.value = Math.max(240, rect.height);
}

onMounted(() => {
  measure();
  resizeObs = new ResizeObserver(() => {
    measure();
    render();
  });
  resizeObs.observe(wrapRef.value);
  render();
});

onBeforeUnmount(() => {
  resizeObs?.disconnect();
  sim?.stop();
});

// 数据变化重渲染
watch(
  () => [props.nodes, props.links],
  () => render(),
  { deep: true }
);
</script>

<style scoped>
.graph-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 480px;
  background: radial-gradient(ellipse at bottom right, rgba(0, 0, 0, 0.04), transparent 60%) no-repeat;
}

.graph-svg {
  width: 100%;
  height: 100%;
  user-select: none;
}

.link {
  stroke: #8aa4c4;
  opacity: 0.9;
}

.link-text {
  font-size: 12px;
  fill: #3a70b3;
  pointer-events: none;
}

.node-label {
  font-size: 12px;
  fill: #333;
  pointer-events: none;
}

marker path {
  stroke: none;
  /* 不指定颜色，沿用线条颜色 */
  fill: currentColor;
}

.tooltip {
  position: absolute;
  background: rgba(20, 24, 36, 0.9);
  color: #fff;
  padding: 8px 10px;
  font-size: 12px;
  border-radius: 8px;
  pointer-events: none;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
  max-width: 240px;
}
.tt-name {
  font-weight: 600;
  margin-bottom: 2px;
}
.tt-sub {
  opacity: 0.9;
}
</style>
