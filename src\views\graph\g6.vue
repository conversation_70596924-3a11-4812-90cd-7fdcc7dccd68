<template>
  <div>
    <div id="container"></div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { Graph, treeToGraphData } from "@antv/g6";
import { fetchGraphData } from "@/api";

let graph = null;
const mockData = ref(null);

const getRandomColor = () => {
  const r = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  const g = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  const b = Math.floor(Math.random() * 256)
    .toString(16)
    .padStart(2, "0");
  return `#${r}${g}${b}`;
};

const getData = async () => {
  const res = await fetchGraphData();
  mockData.value = res.data;
  // 为每个节点添加颜色属性
  const addColor = (node) => {
    // 你可以用任意逻辑生成颜色，下面示例是随机颜色
    node.style = {
      fill: getRandomColor(),
    };
    if (node.children) {
      node.children.forEach(addColor);
    }
  };

  addColor(mockData.value); // 为整个数据结构的每个节点添加颜色属性
  console.log(treeToGraphData(mockData.value));
  console.log(mockData.value);
};

onMounted(async () => {
  await getData();
  graph = new Graph({
    container: document.getElementById("container"),
    autoFit: "view",
    data: treeToGraphData(mockData.value),
    behaviors: ["drag-canvas", "zoom-canvas", "drag-element"],
    node: {
      style: {
        labelText: (d) => d.id,
        // labelBackground: true,
      },
      animation: {
        enter: false,
      },
    },
    layout: {
      type: "dendrogram",
      radial: true,
      nodeSep: 40,
      rankSep: 140,
      gpuEnabled: true,
    },
  });
  graph.render();

  // 为节点添加点击事件
  graph.on("node:click", (evt) => {
    const nodeItem = evt.item; // 获取点击的节点对象
    const nodeModel = nodeItem.getModel(); // 获取节点的数据
    console.log("节点被点击了：", nodeModel);

    // 你可以在这里添加其他逻辑，比如弹出提示框或者修改节点的样式
    graph.setItemState(nodeItem, "selected", true); // 设置节点为选中状态
  });

  // 为边添加点击事件
  graph.on("edge:click", (evt) => {
    const edgeItem = evt.item; // 获取点击的边对象
    const edgeModel = edgeItem.getModel(); // 获取边的数据
    console.log("边被点击了：", edgeModel);

    // 你可以在这里添加其他逻辑，比如高亮显示该边
    graph.setItemState(edgeItem, "selected", true);
  });
});
</script>

<style scoped>
#container {
  width: 100%;
  height: calc(100vh - 48px);
}
</style>
